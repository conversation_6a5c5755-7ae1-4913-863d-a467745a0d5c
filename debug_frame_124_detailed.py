#!/usr/bin/env python3
"""
Frame_00124继承错误详细分析脚本

根据代码分析发现的问题：
1. 区域2没有专门的优先级继承逻辑（不像区域4和区域6）
2. 区域2使用的是基础的跨区域继承规则：2←1
3. 区域2的'1八'从区域1继承了'2八'的ID，但应该从区域6继承'3八'的ID

核心问题：区域2缺少优先级继承逻辑，没有考虑从区域6继承的可能性
"""

import json
import sys
from pathlib import Path

def load_frame_data(frame_num):
    """加载指定帧的数据"""
    frame_path = Path(f"output/labels/frame_{frame_num:05d}.json")
    if not frame_path.exists():
        print(f"❌ 文件不存在: {frame_path}")
        return None
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_cards_by_region_and_label(data):
    """按区域和标签提取卡牌信息"""
    cards_by_region = {}
    
    for shape in data.get('shapes', []):
        region_name = shape.get('region_name', '')
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        
        # 解析区域ID
        region_id = None
        if '手牌_观战方' in region_name:
            region_id = 1
        elif '调整手牌_观战方' in region_name:
            region_id = 2
        elif '抓牌_观战方' in region_name:
            region_id = 3
        elif '打牌_观战方' in region_name:
            region_id = 4
        elif '弃牌_观战方' in region_name:
            region_id = 5
        elif '吃碰区_观战方' in region_name:
            region_id = 6
        elif '抓牌_对战方' in region_name:
            region_id = 7
        elif '打牌_对战方' in region_name:
            region_id = 8
        elif '弃牌_对战方' in region_name:
            region_id = 9
        elif '吃碰区_对战方' in region_name:
            region_id = 16
        
        if region_id is not None:
            if region_id not in cards_by_region:
                cards_by_region[region_id] = {}
            
            if label not in cards_by_region[region_id]:
                cards_by_region[region_id][label] = []
            
            cards_by_region[region_id][label].append({
                'twin_id': twin_id,
                'region_name': region_name,
                'points': shape.get('points', [])
            })
    
    return cards_by_region

def analyze_inheritance_chain():
    """分析Frame_00122->123->124的继承链"""
    print("🔍 Frame_00122->123->124继承链分析")
    print("=" * 60)
    
    # 加载数据
    frame_122 = load_frame_data(122)
    frame_123 = load_frame_data(123)
    frame_124 = load_frame_data(124)
    
    if not all([frame_122, frame_123, frame_124]):
        print("❌ 无法加载必要的帧数据")
        return
    
    # 提取卡牌数据
    cards_122 = extract_cards_by_region_and_label(frame_122)
    cards_123 = extract_cards_by_region_and_label(frame_123)
    cards_124 = extract_cards_by_region_and_label(frame_124)
    
    print("📋 Frame_00122状态:")
    print_ba_cards(cards_122, "Frame_00122")
    
    print("\n📋 Frame_00123状态:")
    print_ba_cards(cards_123, "Frame_00123")
    
    print("\n📋 Frame_00124状态:")
    print_ba_cards(cards_124, "Frame_00124")
    
    # 分析继承逻辑
    analyze_inheritance_logic(cards_122, cards_123, cards_124)

def print_ba_cards(cards_by_region, frame_name):
    """打印包含'八'的卡牌"""
    for region_id in sorted(cards_by_region.keys()):
        region_cards = cards_by_region[region_id]
        for label, card_list in region_cards.items():
            if '八' in label:
                for card in card_list:
                    region_name = get_region_name(region_id)
                    print(f"  区域{region_id:2d} ({region_name}): '{label}' → ID='{card['twin_id']}'")

def get_region_name(region_id):
    """获取区域名称"""
    region_names = {
        1: "手牌区",
        2: "调整区",
        3: "抓牌区",
        4: "打牌区",
        5: "弃牌区",
        6: "吃碰区",
        7: "对战方抓牌区",
        8: "对战方打牌区",
        9: "对战方弃牌区",
        16: "对战方吃碰区"
    }
    return region_names.get(region_id, f"区域{region_id}")

def analyze_inheritance_logic(cards_122, cards_123, cards_124):
    """分析继承逻辑问题"""
    print(f"\n🔄 继承逻辑问题分析:")
    print("=" * 60)

    print("1️⃣ Frame_00122 -> Frame_00123:")
    print("   源数据: 区域1有'1八'(ID:1八)和'2八'(ID:2八)")
    print("   结果: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:3八) ✅")
    print("   分析: 区域6正确从区域1继承了'2八'，并分配了新ID '3八'")

    print("\n2️⃣ Frame_00123 -> Frame_00124:")
    print("   源数据: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:3八)")
    print("   结果: 区域1有'1八'(ID:1八), 区域2有'1八'(ID:2八), 区域6有'2八'(ID:3八)")
    print("   ❌ 问题: 区域2的'1八'继承了ID '2八'，但应该继承区域6的ID '3八'")
    
    print(f"\n🚨 根本问题分析:")
    print("=" * 60)
    print("1. 区域2缺少优先级继承逻辑:")
    print("   - 区域4有_process_region_4_priority_inheritance方法")
    print("   - 区域6有_process_region_6_priority_inheritance方法")
    print("   - 区域2没有专门的优先级继承方法")
    
    print("\n2. 区域2使用基础跨区域继承规则:")
    print("   - 跨区域继承规则: 2←1 (区域2从区域1继承)")
    print("   - 区域2的'1八'从区域1的'2八'继承了ID '2八'")
    print("   - 但没有考虑从区域6继承更合适的ID '3八'")
    
    print("\n3. 继承优先级设计缺陷:")
    print("   - 应该优先从同一张物理牌的历史ID继承")
    print("   - 区域2的'1八'应该继承区域6的'2八'的ID，因为它们是同一张牌")
    print("   - 当前逻辑只考虑了标签匹配，没有考虑牌的连续性")

def propose_solution():
    """提出修复方案"""
    print(f"\n💡 修复方案:")
    print("=" * 60)
    
    print("方案1: 为区域2实现优先级继承逻辑")
    print("   - 创建_process_region_2_priority_inheritance方法")
    print("   - 优先级1: 本区域状态继承（2区域 → 2区域）")
    print("   - 优先级2: 从区域6继承（6区域 → 2区域）")
    print("   - 优先级3: 从区域1继承（1区域 → 2区域）")
    
    print("\n方案2: 修改跨区域继承规则")
    print("   - 将区域2的继承规则从 2←1 改为 2←[6,1]")
    print("   - 优先从区域6继承，其次从区域1继承")
    print("   - 选择ID最大的卡牌进行继承")
    
    print("\n方案3: 增强继承匹配逻辑")
    print("   - 不仅考虑标签匹配，还要考虑牌的连续性")
    print("   - 如果区域6有相同基础标签的牌，优先继承")
    print("   - 实现更智能的继承策略")
    
    print(f"\n🎯 推荐方案: 方案1 + 方案2")
    print("=" * 60)
    print("1. 为区域2实现专门的优先级继承逻辑")
    print("2. 修改跨区域继承规则，让区域2能从区域6继承")
    print("3. 确保处理顺序正确，避免被RegionTransitioner覆盖")

def main():
    """主函数"""
    print("🔍 Frame_00124继承错误详细分析")
    print("=" * 60)
    
    # 分析继承链
    analyze_inheritance_chain()
    
    # 提出修复方案
    propose_solution()
    
    print(f"\n📝 总结:")
    print("=" * 60)
    print("Frame_00124的继承错误是由于区域2缺少优先级继承逻辑造成的。")
    print("区域2的'1八'应该从区域6的'2八'继承ID '3八'，而不是从区域1继承ID '2八'。")
    print("需要为区域2实现类似区域4和区域6的优先级继承机制。")

if __name__ == "__main__":
    main()
