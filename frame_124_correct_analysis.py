#!/usr/bin/env python3
"""
Frame_00124继承错误正确分析脚本

基于正确的输出路径：output/calibration_gt_final_with_digital_twin/labels/

问题描述：
Frame_00123: 区域6有'2八'(ID:2八) ✅ 正确继承
Frame_00124: 区域6有'1八'(ID:1八) ❌ 错误！应该继承上一帧区域6的'2八'的ID

这是一个典型的区域内继承失败问题
"""

import json
import sys
from pathlib import Path

def load_frame_data(frame_num):
    """加载指定帧的数据"""
    frame_path = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json")
    if not frame_path.exists():
        print(f"❌ 文件不存在: {frame_path}")
        return None
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_ba_cards(data, frame_name):
    """提取包含'八'的卡牌信息"""
    ba_cards = []
    
    for shape in data.get('shapes', []):
        label = shape.get('label', '')
        if '八' in label:
            region_name = shape.get('region_name', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            group_id = shape.get('group_id', 0)
            
            ba_cards.append({
                'frame': frame_name,
                'group_id': group_id,
                'region_name': region_name,
                'label': label,
                'twin_id': twin_id,
                'points': shape.get('points', [])
            })
    
    return ba_cards

def analyze_inheritance_problem():
    """分析Frame_00124的继承问题"""
    print("🔍 Frame_00124继承错误深度分析")
    print("=" * 60)
    
    # 加载数据
    frame_122 = load_frame_data(122)
    frame_123 = load_frame_data(123)
    frame_124 = load_frame_data(124)
    
    if not all([frame_122, frame_123, frame_124]):
        print("❌ 无法加载必要的帧数据")
        return
    
    # 提取'八'卡牌
    ba_122 = extract_ba_cards(frame_122, "Frame_00122")
    ba_123 = extract_ba_cards(frame_123, "Frame_00123")
    ba_124 = extract_ba_cards(frame_124, "Frame_00124")
    
    print("📋 Frame_00122中的'八'卡牌:")
    for card in ba_122:
        if not card['twin_id'].startswith('虚拟'):
            print(f"  区域{card['group_id']:2d} ({card['region_name']}): '{card['label']}' → ID='{card['twin_id']}'")
    
    print("\n📋 Frame_00123中的'八'卡牌:")
    for card in ba_123:
        print(f"  区域{card['group_id']:2d} ({card['region_name']}): '{card['label']}' → ID='{card['twin_id']}'")
    
    print("\n📋 Frame_00124中的'八'卡牌:")
    for card in ba_124:
        print(f"  区域{card['group_id']:2d} ({card['region_name']}): '{card['label']}' → ID='{card['twin_id']}'")
    
    # 分析继承问题
    analyze_specific_problem(ba_122, ba_123, ba_124)

def analyze_specific_problem(ba_122, ba_123, ba_124):
    """分析具体的继承问题"""
    print(f"\n🚨 继承问题分析:")
    print("=" * 60)
    
    print("1️⃣ Frame_00122 -> Frame_00123:")
    print("   Frame_00122: 区域1有'1八'(ID:1八)和'2八'(ID:2八)")
    print("   Frame_00123: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:2八)")
    print("   ✅ 正确: 区域6从区域1继承了'2八'，保持了ID连续性")
    
    print("\n2️⃣ Frame_00123 -> Frame_00124:")
    print("   Frame_00123: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:2八)")
    print("   Frame_00124: 区域1有'1八'(ID:1八), 区域6有'1八'(ID:1八), 区域2有'1八'(ID:1八)")
    print("   ❌ 错误: 区域6的'1八'应该继承上一帧区域6的'2八'的ID，而不是继承区域1的'1八'的ID")
    
    print(f"\n🔍 核心问题:")
    print("=" * 60)
    print("Frame_00124中区域6的卡牌发生了错误的继承:")
    print("- 当前: 区域6的'1八' → ID='1八' (错误地继承了区域1的ID)")
    print("- 期望: 区域6的'1八' → ID='2八' (应该继承上一帧区域6的ID)")
    
    print(f"\n🚨 根本原因分析:")
    print("=" * 60)
    print("1. 区域6优先级继承逻辑失效:")
    print("   - 区域6有_process_region_6_priority_inheritance方法")
    print("   - 但在Frame_00124中没有正确执行本区域优先继承")
    print("   - 可能是因为标签从'2八'变为'1八'，导致匹配失败")
    
    print("\n2. 跨区域继承覆盖了正确结果:")
    print("   - SimpleInheritor可能正确处理了区域6的继承")
    print("   - 但RegionTransitioner的跨区域流转覆盖了正确结果")
    print("   - 区域1的'1八'流转到区域6，覆盖了原有的ID")
    
    print("\n3. 标签变化导致的匹配问题:")
    print("   - Frame_00123: 区域6有'2八'")
    print("   - Frame_00124: 区域6有'1八'")
    print("   - 标签变化可能导致基于标签的匹配失败")
    print("   - 需要更智能的匹配策略，考虑位置和上下文")

def propose_solutions():
    """提出修复方案"""
    print(f"\n💡 修复方案讨论:")
    print("=" * 60)
    
    print("方案1: 增强区域6的本区域优先继承")
    print("   - 修改_process_region_6_priority_inheritance方法")
    print("   - 不仅基于标签匹配，还要考虑位置匹配")
    print("   - 如果位置相近，即使标签不同也应该继承")
    
    print("\n方案2: 修复RegionTransitioner的覆盖问题")
    print("   - 在RegionTransitioner中检查保护标记")
    print("   - 如果卡牌已被SimpleInheritor正确处理，不要覆盖")
    print("   - 特别是区域6的本区域继承结果")
    
    print("\n方案3: 改进继承匹配策略")
    print("   - 实现基于位置的智能匹配")
    print("   - 考虑卡牌的空间连续性")
    print("   - 不仅依赖标签，还要考虑位置和上下文")
    
    print("\n方案4: 调整处理顺序")
    print("   - 确保SimpleInheritor的区域内继承优先级最高")
    print("   - RegionTransitioner只处理真正的跨区域流转")
    print("   - 避免不必要的覆盖操作")
    
    print(f"\n🎯 推荐方案: 方案1 + 方案2")
    print("=" * 60)
    print("1. 增强区域6的位置匹配能力，处理标签变化的情况")
    print("2. 在RegionTransitioner中添加保护机制，避免覆盖正确的继承结果")
    print("3. 这样既能解决当前问题，又不会影响其他已修复的帧")

def main():
    """主函数"""
    analyze_inheritance_problem()
    propose_solutions()
    
    print(f"\n📝 总结:")
    print("=" * 60)
    print("Frame_00124的问题是区域6的'1八'错误地继承了区域1的ID，")
    print("而不是继承上一帧区域6的'2八'的ID。这可能是由于标签变化")
    print("导致的匹配失败，或者RegionTransitioner覆盖了正确的继承结果。")
    print("需要增强区域6的本区域优先继承能力，并防止错误的跨区域覆盖。")

if __name__ == "__main__":
    main()
