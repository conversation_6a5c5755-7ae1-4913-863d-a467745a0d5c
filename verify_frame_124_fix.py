#!/usr/bin/env python3
"""
验证Frame_00124修复结果的脚本

检查纯ID继承策略是否成功解决了Frame_00124的继承错误问题
"""

import json
import sys
from pathlib import Path

def load_frame_data(frame_num):
    """加载指定帧的数据"""
    frame_path = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json")
    if not frame_path.exists():
        print(f"❌ 文件不存在: {frame_path}")
        return None
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_ba_cards_by_region(data, frame_name):
    """按区域提取包含'八'的卡牌信息"""
    ba_cards_by_region = {}
    
    for shape in data.get('shapes', []):
        label = shape.get('label', '')
        if '八' in label:
            region_name = shape.get('region_name', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            group_id = shape.get('group_id', 0)
            
            # 解析区域ID
            region_id = None
            if '手牌_观战方' in region_name:
                region_id = 1
            elif '调整手牌_观战方' in region_name:
                region_id = 2
            elif '抓牌_观战方' in region_name:
                region_id = 3
            elif '打牌_观战方' in region_name:
                region_id = 4
            elif '弃牌_观战方' in region_name:
                region_id = 5
            elif '吃碰区_观战方' in region_name:
                region_id = 6
            elif '抓牌_对战方' in region_name:
                region_id = 7
            elif '打牌_对战方' in region_name:
                region_id = 8
            elif '弃牌_对战方' in region_name:
                region_id = 9
            elif '吃碰区_对战方' in region_name:
                region_id = 16
            
            if region_id is not None:
                if region_id not in ba_cards_by_region:
                    ba_cards_by_region[region_id] = []
                
                ba_cards_by_region[region_id].append({
                    'frame': frame_name,
                    'region_id': region_id,
                    'region_name': region_name,
                    'label': label,
                    'twin_id': twin_id,
                    'points': shape.get('points', [])
                })
    
    return ba_cards_by_region

def verify_frame_124_fix():
    """验证Frame_00124的修复结果"""
    print("🔍 验证Frame_00124修复结果")
    print("=" * 60)
    
    # 加载数据
    frame_122 = load_frame_data(122)
    frame_123 = load_frame_data(123)
    frame_124 = load_frame_data(124)
    
    if not all([frame_122, frame_123, frame_124]):
        print("❌ 无法加载必要的帧数据")
        return False
    
    # 提取'八'卡牌
    ba_122 = extract_ba_cards_by_region(frame_122, "Frame_00122")
    ba_123 = extract_ba_cards_by_region(frame_123, "Frame_00123")
    ba_124 = extract_ba_cards_by_region(frame_124, "Frame_00124")
    
    print("📋 Frame_00122中的'八'卡牌:")
    print_ba_cards_by_region(ba_122)
    
    print("\n📋 Frame_00123中的'八'卡牌:")
    print_ba_cards_by_region(ba_123)
    
    print("\n📋 Frame_00124中的'八'卡牌:")
    print_ba_cards_by_region(ba_124)
    
    # 验证修复结果
    return analyze_fix_result(ba_122, ba_123, ba_124)

def print_ba_cards_by_region(ba_cards_by_region):
    """打印按区域分组的'八'卡牌"""
    if not ba_cards_by_region:
        print("  无'八'卡牌")
        return
    
    for region_id in sorted(ba_cards_by_region.keys()):
        cards = ba_cards_by_region[region_id]
        region_name = get_region_name(region_id)
        print(f"  区域{region_id:2d} ({region_name}):")
        for card in cards:
            print(f"    '{card['label']}' → ID='{card['twin_id']}'")

def get_region_name(region_id):
    """获取区域名称"""
    region_names = {
        1: "手牌区",
        2: "调整区", 
        3: "抓牌区",
        4: "打牌区",
        5: "弃牌区",
        6: "吃碰区",
        7: "对战方抓牌区",
        8: "对战方打牌区",
        9: "对战方弃牌区",
        16: "对战方吃碰区"
    }
    return region_names.get(region_id, f"区域{region_id}")

def analyze_fix_result(ba_122, ba_123, ba_124):
    """分析修复结果"""
    print(f"\n🔍 修复结果分析:")
    print("=" * 60)
    
    # 检查Frame_00124中是否还存在原来的问题
    region_6_cards = ba_124.get(6, [])
    region_2_cards = ba_124.get(2, [])
    
    print("1️⃣ 检查原问题是否解决:")
    
    # 原问题：区域6的'1八'应该继承上一帧区域6的'2八'的ID
    if region_6_cards:
        print(f"   区域6有{len(region_6_cards)}张'八'卡牌:")
        for card in region_6_cards:
            print(f"     '{card['label']}' → ID='{card['twin_id']}'")
        
        # 检查是否有'1八'继承了错误的ID
        problem_found = False
        for card in region_6_cards:
            if card['label'] == '1八' and card['twin_id'] == '1八':
                print(f"   ❌ 仍存在问题: 区域6的'1八'继承了ID='1八'，应该继承区域6上一帧的ID")
                problem_found = True
        
        if not problem_found:
            print(f"   ✅ 区域6的继承看起来正常")
    else:
        print(f"   ℹ️  区域6没有'八'卡牌")
    
    # 检查区域2的情况
    if region_2_cards:
        print(f"   区域2有{len(region_2_cards)}张'八'卡牌:")
        for card in region_2_cards:
            print(f"     '{card['label']}' → ID='{card['twin_id']}'")
            if card['label'] == '1八' and card['twin_id'] == '2八':
                print(f"   ❌ 仍存在问题: 区域2的'1八'继承了ID='2八'，应该继承更合适的ID")
    else:
        print(f"   ✅ 区域2没有'八'卡牌（符合预期）")
    
    print("\n2️⃣ 纯ID继承策略效果评估:")
    
    # 统计ID继承情况
    all_ba_cards = []
    for region_cards in ba_124.values():
        all_ba_cards.extend(region_cards)
    
    id_inheritance_count = 0
    total_cards = len(all_ba_cards)
    
    for card in all_ba_cards:
        # 检查是否是ID直接继承（标签和ID不同，说明继承了其他卡牌的ID）
        if card['label'] != card['twin_id']:
            id_inheritance_count += 1
    
    if total_cards > 0:
        id_inheritance_rate = (id_inheritance_count / total_cards) * 100
        print(f"   ID继承率: {id_inheritance_rate:.1f}% ({id_inheritance_count}/{total_cards})")
        
        if id_inheritance_rate > 50:
            print(f"   ✅ 纯ID继承策略生效，大部分卡牌通过ID继承")
        else:
            print(f"   ⚠️  ID继承率较低，可能仍在使用标签匹配")
    
    print("\n3️⃣ 总体评估:")
    
    # 检查是否解决了原始问题
    original_problem_solved = True
    
    # 原问题的特征：区域6的'1八'错误继承了区域1的ID
    for card in region_6_cards:
        if card['label'] == '1八' and card['twin_id'] == '1八':
            original_problem_solved = False
            break
    
    # 检查区域2是否还有错误继承
    for card in region_2_cards:
        if card['label'] == '1八' and card['twin_id'] == '2八':
            original_problem_solved = False
            break
    
    if original_problem_solved:
        print("   ✅ Frame_00124的继承错误问题已解决")
        print("   ✅ 纯ID继承策略成功实施")
        return True
    else:
        print("   ❌ Frame_00124的继承错误问题仍然存在")
        print("   ❌ 需要进一步调试纯ID继承策略")
        return False

def main():
    """主函数"""
    success = verify_frame_124_fix()
    
    print(f"\n📝 验证结论:")
    print("=" * 60)
    if success:
        print("🎉 Frame_00124修复验证成功！")
        print("🔧 纯ID继承策略有效解决了标签变化导致的继承错误问题")
        print("💡 系统现在能够基于数字孪生ID进行准确的继承匹配")
    else:
        print("⚠️  Frame_00124修复验证失败")
        print("🔧 需要进一步分析和调试纯ID继承策略")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
