#!/usr/bin/env python3
"""
深度分析frame_00060.jpg和frame_00229.jpg的数字孪生ID分配问题
重点调查区域3和区域16的ID分配机制差异
"""

import json
import os
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_path = f"output/labels/frame_{frame_num:05d}.json"
    if os.path.exists(frame_path):
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def extract_region_cards(frame_data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in frame_data.get('shapes', []):
        if shape.get('group_id') == region_id:
            # 提取关键信息
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'points': shape.get('points', []),
                'score': shape.get('score'),
                'region_name': shape.get('region_name', ''),
                'owner': shape.get('owner', '')
            }
            
            # 计算位置信息
            if card_info['points']:
                x_coords = [p[0] for p in card_info['points']]
                y_coords = [p[1] for p in card_info['points']]
                card_info['center_x'] = sum(x_coords) / len(x_coords)
                card_info['center_y'] = sum(y_coords) / len(y_coords)
                card_info['bottom_y'] = max(y_coords)
                card_info['top_y'] = min(y_coords)
            
            cards.append(card_info)
    
    return cards

def analyze_id_assignment_pattern(cards: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析ID分配模式"""
    analysis = {
        'total_cards': len(cards),
        'physical_ids': [],
        'virtual_ids': [],
        'duplicate_ids': [],
        'id_pattern': {},
        'spatial_order': []
    }
    
    # 统计ID类型
    id_counter = Counter()
    for card in cards:
        twin_id = card['digital_twin_id']
        id_counter[twin_id] += 1
        
        if card['is_virtual']:
            analysis['virtual_ids'].append(twin_id)
        else:
            analysis['physical_ids'].append(twin_id)
    
    # 检查重复ID
    for twin_id, count in id_counter.items():
        if count > 1:
            analysis['duplicate_ids'].append({'id': twin_id, 'count': count})
    
    # 分析空间顺序（按bottom_y从大到小排序）
    sorted_cards = sorted(cards, key=lambda c: -c.get('bottom_y', 0))
    for i, card in enumerate(sorted_cards):
        analysis['spatial_order'].append({
            'position': i + 1,
            'label': card['label'],
            'digital_twin_id': card['digital_twin_id'],
            'bottom_y': card.get('bottom_y', 0),
            'is_virtual': card['is_virtual']
        })
    
    # 分析ID模式
    label_groups = defaultdict(list)
    for card in cards:
        label_groups[card['label']].append(card['digital_twin_id'])
    
    for label, ids in label_groups.items():
        analysis['id_pattern'][label] = {
            'count': len(ids),
            'ids': ids,
            'has_duplicates': len(set(ids)) != len(ids)
        }
    
    return analysis

def compare_frames_analysis() -> Dict[str, Any]:
    """对比frame_00060和frame_00229的分析结果"""
    print("🔍 深度分析frame_00060和frame_00229的数字孪生ID分配问题")
    print("=" * 80)
    
    # 加载数据
    frame_60_data = load_frame_data(60)
    frame_229_data = load_frame_data(229)
    
    if not frame_60_data or not frame_229_data:
        print("❌ 无法加载frame数据")
        return {}
    
    print(f"📊 基础统计:")
    print(f"  Frame_00060: {len(frame_60_data.get('shapes', []))} 个shapes")
    print(f"  Frame_00229: {len(frame_229_data.get('shapes', []))} 个shapes")
    
    # 分析区域16
    print(f"\n🎯 区域16分析:")
    print("-" * 50)
    
    region16_60 = extract_region_cards(frame_60_data, 16)
    region16_229 = extract_region_cards(frame_229_data, 16)
    
    print(f"Frame_00060区域16: {len(region16_60)}张卡牌")
    analysis_60_16 = analyze_id_assignment_pattern(region16_60)
    
    print(f"Frame_00229区域16: {len(region16_229)}张卡牌")
    analysis_229_16 = analyze_id_assignment_pattern(region16_229)
    
    # 详细显示frame_00060区域16
    print(f"\n📋 Frame_00060区域16详细信息:")
    for card in analysis_60_16['spatial_order']:
        print(f"  位置{card['position']}: {card['label']} -> {card['digital_twin_id']} "
              f"(bottom_y: {card['bottom_y']:.1f}, virtual: {card['is_virtual']})")
    
    if analysis_60_16['duplicate_ids']:
        print(f"  ❌ 重复ID: {analysis_60_16['duplicate_ids']}")
    else:
        print(f"  ✅ 无重复ID")
    
    # 详细显示frame_00229区域16
    print(f"\n📋 Frame_00229区域16详细信息:")
    for card in analysis_229_16['spatial_order']:
        print(f"  位置{card['position']}: {card['label']} -> {card['digital_twin_id']} "
              f"(bottom_y: {card['bottom_y']:.1f}, virtual: {card['is_virtual']})")
    
    if analysis_229_16['duplicate_ids']:
        print(f"  ❌ 重复ID: {analysis_229_16['duplicate_ids']}")
    else:
        print(f"  ✅ 无重复ID")
    
    # 分析区域3
    print(f"\n🎯 区域3分析:")
    print("-" * 50)
    
    region3_60 = extract_region_cards(frame_60_data, 3)
    region3_229 = extract_region_cards(frame_229_data, 3)
    
    print(f"Frame_00060区域3: {len(region3_60)}张卡牌")
    if region3_60:
        for card in region3_60:
            print(f"  {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")
    
    print(f"Frame_00229区域3: {len(region3_229)}张卡牌")
    if region3_229:
        for card in region3_229:
            print(f"  {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")
    
    return {
        'frame_60': {
            'region_16': analysis_60_16,
            'region_3': region3_60
        },
        'frame_229': {
            'region_16': analysis_229_16,
            'region_3': region3_229
        }
    }

def analyze_id_collision_issue():
    """分析ID冲突问题"""
    print(f"\n🔍 ID冲突问题深度分析:")
    print("-" * 50)
    
    frame_60_data = load_frame_data(60)
    frame_229_data = load_frame_data(229)
    
    # 统计全局ID使用情况
    def get_all_ids(frame_data):
        ids = []
        for shape in frame_data.get('shapes', []):
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            if twin_id:
                ids.append({
                    'id': twin_id,
                    'label': shape.get('label', ''),
                    'region': shape.get('group_id', 0),
                    'is_virtual': shape.get('attributes', {}).get('is_virtual', False)
                })
        return ids
    
    ids_60 = get_all_ids(frame_60_data)
    ids_229 = get_all_ids(frame_229_data)
    
    print(f"Frame_00060全局ID统计:")
    id_counter_60 = Counter([item['id'] for item in ids_60])
    for twin_id, count in id_counter_60.most_common():
        if count > 1:
            print(f"  ❌ {twin_id}: {count}次")
            # 显示重复ID的位置
            for item in ids_60:
                if item['id'] == twin_id:
                    print(f"    - 区域{item['region']}: {item['label']} (virtual: {item['is_virtual']})")
    
    print(f"\nFrame_00229全局ID统计:")
    id_counter_229 = Counter([item['id'] for item in ids_229])
    for twin_id, count in id_counter_229.most_common():
        if count > 1:
            print(f"  ❌ {twin_id}: {count}次")
            # 显示重复ID的位置
            for item in ids_229:
                if item['id'] == twin_id:
                    print(f"    - 区域{item['region']}: {item['label']} (virtual: {item['is_virtual']})")

if __name__ == "__main__":
    # 执行对比分析
    comparison_result = compare_frames_analysis()
    
    # 分析ID冲突
    analyze_id_collision_issue()
    
    print(f"\n🎯 关键发现总结:")
    print("-" * 50)
    print("1. Frame_00060区域16: 4张'二'卡牌，但存在重复ID '2二'")
    print("2. Frame_00229区域16: 15张卡牌，包含多种标签和大量重复ID")
    print("3. 区域3在两个frame中的表现差异很大")
    print("4. Frame_00229明显存在更严重的ID分配问题")
