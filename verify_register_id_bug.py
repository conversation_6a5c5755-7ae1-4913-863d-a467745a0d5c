#!/usr/bin/env python3
"""
验证register_id方法调用错误的脚本
检查basic_id_assigner.py中的方法调用问题
"""

import re
import os
from typing import List, Tuple

def analyze_register_id_calls() -> List[Tuple[int, str, bool]]:
    """分析register_id方法的调用情况"""
    file_path = "src/modules/basic_id_assigner.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找register_id方法定义
    method_definition = None
    for i, line in enumerate(lines):
        if "def register_id(self, twin_id: str, card_info: Dict[str, Any]):" in line:
            method_definition = (i + 1, line.strip())
            break
    
    if not method_definition:
        print("❌ 未找到register_id方法定义")
        return []
    
    print(f"✅ 找到register_id方法定义:")
    print(f"   第{method_definition[0]}行: {method_definition[1]}")
    print(f"   📋 方法签名: register_id(self, twin_id: str, card_info: Dict[str, Any])")
    print(f"   📋 需要参数: twin_id, card_info")
    
    # 查找所有register_id方法调用
    call_pattern = r'\.register_id\('
    calls = []
    
    for i, line in enumerate(lines):
        if re.search(call_pattern, line):
            line_num = i + 1
            call_text = line.strip()
            
            # 分析参数数量
            # 简单的参数计数（不处理复杂的嵌套情况）
            call_start = line.find('.register_id(')
            if call_start != -1:
                call_part = line[call_start:]
                # 提取括号内的内容
                paren_start = call_part.find('(')
                paren_end = call_part.find(')')
                if paren_start != -1 and paren_end != -1:
                    params_text = call_part[paren_start+1:paren_end].strip()
                    
                    # 计算参数数量（简单的逗号分割）
                    if not params_text:
                        param_count = 0
                    else:
                        param_count = len([p.strip() for p in params_text.split(',') if p.strip()])
                    
                    # 判断是否正确（需要2个参数：twin_id, card_info）
                    is_correct = param_count == 2
                    
                    calls.append((line_num, call_text, is_correct, param_count, params_text))
    
    return calls

def check_specific_problematic_calls():
    """检查特定的问题调用"""
    file_path = "src/modules/basic_id_assigner.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找特定的错误模式
    problematic_patterns = [
        r'self\.global_id_manager\.register_id\([^,)]+\)',  # 只有一个参数的调用
        r'\.register_id\(next_id\)',  # 只传next_id的调用
        r'\.register_id\(target_id\)',  # 只传target_id的调用
        r'\.register_id\(available_id\)',  # 只传available_id的调用
    ]
    
    print(f"\n🔍 检查特定的问题模式:")
    
    for pattern in problematic_patterns:
        matches = re.finditer(pattern, content, re.MULTILINE)
        for match in matches:
            # 计算行号
            line_num = content[:match.start()].count('\n') + 1
            matched_text = match.group()
            print(f"   ❌ 第{line_num}行: {matched_text}")

def main():
    print("🔍 验证register_id方法调用错误")
    print("=" * 60)
    
    # 分析所有register_id调用
    calls = analyze_register_id_calls()
    
    if not calls:
        print("❌ 未找到register_id方法调用")
        return
    
    print(f"\n📊 找到 {len(calls)} 个register_id方法调用:")
    print("-" * 60)
    
    correct_calls = 0
    incorrect_calls = 0
    
    for line_num, call_text, is_correct, param_count, params_text in calls:
        status = "✅" if is_correct else "❌"
        print(f"{status} 第{line_num}行: {call_text}")
        print(f"   参数数量: {param_count} (期望: 2)")
        print(f"   参数内容: '{params_text}'")
        
        if is_correct:
            correct_calls += 1
        else:
            incorrect_calls += 1
            print(f"   🔧 问题: 缺少card_info参数")
        print()
    
    # 检查特定问题模式
    check_specific_problematic_calls()
    
    # 总结
    print(f"\n📋 总结:")
    print(f"   总调用数: {len(calls)}")
    print(f"   正确调用: {correct_calls}")
    print(f"   错误调用: {incorrect_calls}")
    
    if incorrect_calls > 0:
        print(f"\n🎯 修复建议:")
        print(f"   1. 将 register_id(twin_id) 改为 register_id(twin_id, card_info)")
        print(f"   2. 确保传入完整的卡牌信息作为第二个参数")
        print(f"   3. 这将修复ID注册失败导致的重复ID问题")
    else:
        print(f"\n✅ 所有register_id调用都是正确的")

if __name__ == "__main__":
    main()
