#!/usr/bin/env python3
"""
全面数据验证脚本

验证frame_00001到frame_00158的输出是否符合设计预期
检查继承逻辑、ID分配、区域流转等关键功能
"""

import json
import sys
from pathlib import Path
from collections import defaultdict
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveValidator:
    def __init__(self):
        self.output_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
        self.validation_results = {
            "total_frames": 0,
            "valid_frames": 0,
            "invalid_frames": 0,
            "issues": [],
            "inheritance_issues": [],
            "id_issues": [],
            "region_issues": []
        }
        
    def load_frame_data(self, frame_num):
        """加载指定帧的数据"""
        frame_path = self.output_dir / f"frame_{frame_num:05d}.json"
        if not frame_path.exists():
            return None
        
        try:
            with open(frame_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载Frame_{frame_num:05d}失败: {e}")
            return None
    
    def extract_cards_info(self, data, frame_num):
        """提取卡牌信息"""
        cards_info = {
            "frame_num": frame_num,
            "total_cards": 0,
            "cards_by_region": defaultdict(list),
            "cards_by_label": defaultdict(list),
            "id_mapping": {},
            "virtual_cards": 0,
            "physical_cards": 0
        }
        
        if not data or 'shapes' not in data:
            return cards_info
        
        for shape in data['shapes']:
            label = shape.get('label', '')
            region_name = shape.get('region_name', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            is_virtual = shape.get('attributes', {}).get('is_virtual', False)
            group_id = shape.get('group_id', 0)
            
            # 统计卡牌
            cards_info["total_cards"] += 1
            if is_virtual:
                cards_info["virtual_cards"] += 1
            else:
                cards_info["physical_cards"] += 1
            
            # 按区域分组
            cards_info["cards_by_region"][group_id].append({
                "label": label,
                "twin_id": twin_id,
                "region_name": region_name,
                "is_virtual": is_virtual,
                "points": shape.get('points', [])
            })
            
            # 按标签分组
            cards_info["cards_by_label"][label].append({
                "twin_id": twin_id,
                "region_id": group_id,
                "is_virtual": is_virtual
            })
            
            # ID映射
            if twin_id:
                cards_info["id_mapping"][twin_id] = {
                    "label": label,
                    "region_id": group_id,
                    "is_virtual": is_virtual
                }
        
        return cards_info
    
    def validate_inheritance_logic(self, prev_frame, curr_frame):
        """验证继承逻辑"""
        issues = []
        
        if not prev_frame or not curr_frame:
            return issues
        
        # 检查ID连续性
        prev_ids = set(prev_frame["id_mapping"].keys())
        curr_ids = set(curr_frame["id_mapping"].keys())
        
        # 检查消失的ID
        disappeared_ids = prev_ids - curr_ids
        new_ids = curr_ids - prev_ids
        
        # 分析ID变化合理性
        if len(disappeared_ids) > 10:  # 如果消失的ID过多，可能有问题
            issues.append({
                "type": "excessive_id_disappearance",
                "frame": curr_frame["frame_num"],
                "disappeared_count": len(disappeared_ids),
                "disappeared_ids": list(disappeared_ids)[:10]  # 只显示前10个
            })
        
        # 检查同一标签的ID分配
        for label, cards in curr_frame["cards_by_label"].items():
            if len(cards) > 1:
                # 同标签多张卡牌，检查ID是否合理
                ids = [card["twin_id"] for card in cards]
                unique_ids = set(ids)
                if len(unique_ids) != len(ids):
                    issues.append({
                        "type": "duplicate_id_for_same_label",
                        "frame": curr_frame["frame_num"],
                        "label": label,
                        "ids": ids
                    })
        
        return issues
    
    def validate_id_format(self, frame_info):
        """验证ID格式"""
        issues = []
        
        for twin_id, card_info in frame_info["id_mapping"].items():
            # 检查ID格式
            if not twin_id:
                issues.append({
                    "type": "empty_id",
                    "frame": frame_info["frame_num"],
                    "label": card_info["label"]
                })
                continue
            
            # 检查虚拟卡牌ID格式
            if card_info["is_virtual"]:
                if not (twin_id.startswith("虚拟") or twin_id.startswith("临时")):
                    issues.append({
                        "type": "invalid_virtual_id_format",
                        "frame": frame_info["frame_num"],
                        "id": twin_id,
                        "label": card_info["label"]
                    })
            else:
                # 物理卡牌ID格式检查
                if twin_id.startswith("虚拟") or twin_id.startswith("临时"):
                    issues.append({
                        "type": "invalid_physical_id_format",
                        "frame": frame_info["frame_num"],
                        "id": twin_id,
                        "label": card_info["label"]
                    })
        
        return issues
    
    def validate_region_distribution(self, frame_info):
        """验证区域分布"""
        issues = []
        
        # 检查区域分布合理性
        total_cards = frame_info["total_cards"]
        if total_cards == 0:
            return issues
        
        # 检查主要区域的卡牌数量
        region_1_count = len(frame_info["cards_by_region"].get(1, []))  # 手牌区
        region_6_count = len(frame_info["cards_by_region"].get(6, []))  # 吃碰区
        region_16_count = len(frame_info["cards_by_region"].get(16, []))  # 对战方吃碰区
        
        # 检查异常的区域分布
        if region_1_count > 25:  # 手牌区卡牌过多
            issues.append({
                "type": "excessive_cards_in_region_1",
                "frame": frame_info["frame_num"],
                "count": region_1_count
            })
        
        # 检查是否有卡牌在不合理的区域
        for region_id, cards in frame_info["cards_by_region"].items():
            if region_id not in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 16]:
                issues.append({
                    "type": "invalid_region_id",
                    "frame": frame_info["frame_num"],
                    "region_id": region_id,
                    "card_count": len(cards)
                })
        
        return issues
    
    def validate_specific_frames(self):
        """验证特定帧的已知问题"""
        specific_issues = []
        
        # 验证Frame_00124（之前的问题帧）
        frame_124 = self.load_frame_data(124)
        if frame_124:
            frame_124_info = self.extract_cards_info(frame_124, 124)
            
            # 检查是否还有'八'卡牌的继承问题
            ba_cards = frame_124_info["cards_by_label"].get("八", []) + \
                      frame_124_info["cards_by_label"].get("1八", []) + \
                      frame_124_info["cards_by_label"].get("2八", [])
            
            if ba_cards:
                # 检查区域6是否有'八'卡牌继承问题
                region_6_ba = [card for card in ba_cards if card["region_id"] == 6]
                region_2_ba = [card for card in ba_cards if card["region_id"] == 2]
                
                if region_6_ba:
                    for card in region_6_ba:
                        if card["twin_id"] == "1八":  # 如果区域6的八继承了1八的ID
                            specific_issues.append({
                                "type": "frame_124_inheritance_issue",
                                "frame": 124,
                                "description": "区域6的八卡牌继承了错误的ID",
                                "card": card
                            })
                
                if region_2_ba:
                    for card in region_2_ba:
                        if card["twin_id"] == "2八":  # 如果区域2的八继承了2八的ID
                            specific_issues.append({
                                "type": "frame_124_inheritance_issue",
                                "frame": 124,
                                "description": "区域2的八卡牌继承了错误的ID",
                                "card": card
                            })
        
        return specific_issues
    
    def validate_frames_range(self, start_frame=1, end_frame=158):
        """验证指定范围的帧"""
        logger.info(f"开始验证Frame_{start_frame:05d}到Frame_{end_frame:05d}")
        
        prev_frame_info = None
        
        for frame_num in range(start_frame, end_frame + 1):
            logger.info(f"验证Frame_{frame_num:05d}")
            
            # 加载帧数据
            frame_data = self.load_frame_data(frame_num)
            if not frame_data:
                self.validation_results["issues"].append({
                    "type": "missing_frame",
                    "frame": frame_num
                })
                continue
            
            # 提取卡牌信息
            frame_info = self.extract_cards_info(frame_data, frame_num)
            self.validation_results["total_frames"] += 1
            
            # 验证继承逻辑
            inheritance_issues = self.validate_inheritance_logic(prev_frame_info, frame_info)
            self.validation_results["inheritance_issues"].extend(inheritance_issues)
            
            # 验证ID格式
            id_issues = self.validate_id_format(frame_info)
            self.validation_results["id_issues"].extend(id_issues)
            
            # 验证区域分布
            region_issues = self.validate_region_distribution(frame_info)
            self.validation_results["region_issues"].extend(region_issues)
            
            # 检查是否有问题
            frame_has_issues = bool(inheritance_issues or id_issues or region_issues)
            
            if frame_has_issues:
                self.validation_results["invalid_frames"] += 1
                logger.warning(f"Frame_{frame_num:05d}发现问题: "
                             f"继承{len(inheritance_issues)}, ID{len(id_issues)}, 区域{len(region_issues)}")
            else:
                self.validation_results["valid_frames"] += 1
            
            prev_frame_info = frame_info
        
        # 验证特定帧的已知问题
        specific_issues = self.validate_specific_frames()
        self.validation_results["issues"].extend(specific_issues)
        
        logger.info(f"验证完成: 总帧数{self.validation_results['total_frames']}, "
                   f"有效{self.validation_results['valid_frames']}, "
                   f"无效{self.validation_results['invalid_frames']}")
    
    def generate_report(self):
        """生成验证报告"""
        print("\n🔍 数据验证报告")
        print("=" * 60)
        
        print(f"📊 总体统计:")
        print(f"   总帧数: {self.validation_results['total_frames']}")
        print(f"   有效帧: {self.validation_results['valid_frames']}")
        print(f"   问题帧: {self.validation_results['invalid_frames']}")
        
        if self.validation_results['total_frames'] > 0:
            success_rate = (self.validation_results['valid_frames'] / self.validation_results['total_frames']) * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        print(f"\n🚨 问题统计:")
        print(f"   继承问题: {len(self.validation_results['inheritance_issues'])}")
        print(f"   ID问题: {len(self.validation_results['id_issues'])}")
        print(f"   区域问题: {len(self.validation_results['region_issues'])}")
        print(f"   其他问题: {len(self.validation_results['issues'])}")
        
        # 详细问题报告
        if self.validation_results['inheritance_issues']:
            print(f"\n📋 继承问题详情:")
            for issue in self.validation_results['inheritance_issues'][:5]:  # 只显示前5个
                print(f"   Frame_{issue['frame']:05d}: {issue['type']}")
        
        if self.validation_results['id_issues']:
            print(f"\n📋 ID问题详情:")
            for issue in self.validation_results['id_issues'][:5]:  # 只显示前5个
                print(f"   Frame_{issue['frame']:05d}: {issue['type']} - {issue.get('id', 'N/A')}")
        
        if self.validation_results['region_issues']:
            print(f"\n📋 区域问题详情:")
            for issue in self.validation_results['region_issues'][:5]:  # 只显示前5个
                print(f"   Frame_{issue['frame']:05d}: {issue['type']}")
        
        # 总结
        total_issues = (len(self.validation_results['inheritance_issues']) + 
                       len(self.validation_results['id_issues']) + 
                       len(self.validation_results['region_issues']) + 
                       len(self.validation_results['issues']))
        
        print(f"\n📝 验证结论:")
        print("=" * 60)
        if total_issues == 0:
            print("✅ 所有帧都符合设计预期，无需修复")
            return True
        else:
            print(f"⚠️  发现{total_issues}个问题，需要进一步分析和修复")
            return False

def main():
    """主函数"""
    validator = ComprehensiveValidator()
    
    # 验证frame_00001到frame_00158
    validator.validate_frames_range(1, 158)
    
    # 生成报告
    success = validator.generate_report()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
