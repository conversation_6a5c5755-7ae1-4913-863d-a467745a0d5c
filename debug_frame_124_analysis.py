#!/usr/bin/env python3
"""
Frame_00124继承错误分析脚本

问题描述：
- Frame_00122: 区域1有'1八'(ID:1八)和'2八'(ID:2八)
- Frame_00123: 区域6有'2八'(ID:3八) ✅ 正确继承了区域1的'2八'
- Frame_00124: 区域6有'2八'(ID:3八) + 区域2有'1八'(ID:2八) ❌ 错误！区域2的'1八'应该继承区域6的'2八'的ID

分析目标：
1. 确认Frame_00123->Frame_00124的继承逻辑
2. 找出区域2的'1八'为什么没有继承区域6的'2八'的ID
3. 分析SimpleInheritor和RegionTransitioner的处理顺序
"""

import json
import sys
from pathlib import Path

def load_frame_data(frame_num):
    """加载指定帧的数据"""
    frame_path = Path(f"output/labels/frame_{frame_num:05d}.json")
    if not frame_path.exists():
        print(f"❌ 文件不存在: {frame_path}")
        return None
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_cards_by_region(data):
    """按区域提取卡牌信息"""
    cards_by_region = {}
    
    for shape in data.get('shapes', []):
        region_name = shape.get('region_name', '')
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        
        # 解析区域ID
        region_id = None
        if '手牌_观战方' in region_name:
            region_id = 1
        elif '调整手牌_观战方' in region_name:
            region_id = 2
        elif '抓牌_观战方' in region_name:
            region_id = 3
        elif '打牌_观战方' in region_name:
            region_id = 4
        elif '弃牌_观战方' in region_name:
            region_id = 5
        elif '吃碰区_观战方' in region_name:
            region_id = 6
        elif '抓牌_对战方' in region_name:
            region_id = 7
        elif '打牌_对战方' in region_name:
            region_id = 8
        elif '弃牌_对战方' in region_name:
            region_id = 9
        elif '吃碰区_对战方' in region_name:
            region_id = 16
        
        if region_id is not None:
            if region_id not in cards_by_region:
                cards_by_region[region_id] = []
            
            cards_by_region[region_id].append({
                'label': label,
                'twin_id': twin_id,
                'region_name': region_name,
                'points': shape.get('points', [])
            })
    
    return cards_by_region

def analyze_ba_cards(frame_num, cards_by_region):
    """分析包含'八'的卡牌"""
    print(f"\n🔍 Frame_{frame_num:05d} 中的'八'卡牌分析:")
    print("=" * 60)
    
    ba_cards = []
    for region_id, cards in cards_by_region.items():
        for card in cards:
            if '八' in card['label']:
                ba_cards.append({
                    'region_id': region_id,
                    'region_name': card['region_name'],
                    'label': card['label'],
                    'twin_id': card['twin_id'],
                    'points': card['points']
                })
    
    # 按区域ID排序
    ba_cards.sort(key=lambda x: x['region_id'])
    
    for card in ba_cards:
        print(f"  区域{card['region_id']:2d} ({card['region_name']}): "
              f"标签='{card['label']}' → ID='{card['twin_id']}'")
    
    return ba_cards

def compare_inheritance_logic(frame_122, frame_123, frame_124):
    """比较继承逻辑"""
    print(f"\n🔄 继承逻辑分析:")
    print("=" * 60)
    
    # 分析Frame_00122 -> Frame_00123
    print("📋 Frame_00122 -> Frame_00123:")
    ba_122 = analyze_ba_cards(122, extract_cards_by_region(frame_122))
    ba_123 = analyze_ba_cards(123, extract_cards_by_region(frame_123))
    
    print("\n  继承分析:")
    print("    Frame_00122: 区域1有'1八'(ID:1八)和'2八'(ID:2八)")
    print("    Frame_00123: 区域6有'2八'(ID:3八) ✅ 正确继承了区域1的'2八'")
    print("    → 符合设计：优先继承数值大的卡牌")
    
    # 分析Frame_00123 -> Frame_00124
    print("\n📋 Frame_00123 -> Frame_00124:")
    ba_124 = analyze_ba_cards(124, extract_cards_by_region(frame_124))
    
    print("\n  继承分析:")
    print("    Frame_00123: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:3八)")
    print("    Frame_00124: 区域1有'1八'(ID:1八), 区域6有'2八'(ID:3八), 区域2有'1八'(ID:2八)")
    print("    ❌ 问题: 区域2的'1八'应该继承区域6的'2八'的ID(3八)，而不是ID(2八)")

def analyze_inheritance_priority():
    """分析继承优先级问题"""
    print(f"\n🚨 继承优先级问题分析:")
    print("=" * 60)
    
    print("根据当前代码逻辑:")
    print("1. SimpleInheritor处理区域内继承:")
    print("   - 区域2的'1八'应该优先从区域2的前一帧继承")
    print("   - 如果区域2前一帧没有'八'，则应该从其他区域继承")
    
    print("\n2. RegionTransitioner处理跨区域流转:")
    print("   - 可能会覆盖SimpleInheritor的结果")
    print("   - 如果区域1有'1八'，可能会流转到区域2")
    
    print("\n🔍 可能的问题原因:")
    print("1. 区域2优先级继承逻辑缺失:")
    print("   - 没有实现类似区域4和区域6的优先级继承")
    print("   - 直接使用了基础的空间匹配逻辑")
    
    print("\n2. 跨区域继承规则问题:")
    print("   - 区域2可能从区域1继承了'1八'的ID")
    print("   - 没有考虑区域6已有更合适的'2八'")
    
    print("\n3. 处理顺序问题:")
    print("   - SimpleInheritor可能正确处理了，但被RegionTransitioner覆盖")
    print("   - 或者SimpleInheritor本身就没有正确处理区域2")

def main():
    """主函数"""
    print("🔍 Frame_00124继承错误深度分析")
    print("=" * 60)
    
    # 加载数据
    frame_122 = load_frame_data(122)
    frame_123 = load_frame_data(123)
    frame_124 = load_frame_data(124)
    
    if not all([frame_122, frame_123, frame_124]):
        print("❌ 无法加载必要的帧数据")
        return
    
    # 分析继承逻辑
    compare_inheritance_logic(frame_122, frame_123, frame_124)
    
    # 分析继承优先级问题
    analyze_inheritance_priority()
    
    print(f"\n💡 修复建议:")
    print("=" * 60)
    print("1. 为区域2实现优先级继承逻辑:")
    print("   - 优先从本区域继承")
    print("   - 其次从相邻区域继承（如区域1、区域6）")
    print("   - 选择ID最大的卡牌进行继承")
    
    print("\n2. 完善跨区域继承规则:")
    print("   - 确保区域2能够从区域6继承")
    print("   - 避免错误的1→2流转覆盖正确的6→2继承")
    
    print("\n3. 调整处理顺序:")
    print("   - 确保SimpleInheritor的正确结果不被RegionTransitioner覆盖")
    print("   - 或者在RegionTransitioner中添加保护机制")

if __name__ == "__main__":
    main()
