#!/usr/bin/env python3
"""
深度分析Frame_00229中区域3和区域16的"三"卡牌ID分配问题
"""

import json
import os
from typing import Dict, List, Any, Tuple
from collections import defaultdict

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的数据"""
    frame_path = f"output/labels/frame_{frame_num:05d}.json"
    if os.path.exists(frame_path):
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def extract_cards_by_label(frame_data: Dict[str, Any], label_pattern: str) -> List[Dict[str, Any]]:
    """提取包含指定标签的卡牌"""
    cards = []
    for shape in frame_data.get('shapes', []):
        if label_pattern in shape.get('label', ''):
            # 提取关键信息
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'group_id': shape.get('group_id', 0),
                'region_name': shape.get('region_name', ''),
                'points': shape.get('points', []),
                'score': shape.get('score'),
                'owner': shape.get('owner', '')
            }
            
            # 计算位置信息
            if card_info['points']:
                x_coords = [p[0] for p in card_info['points']]
                y_coords = [p[1] for p in card_info['points']]
                card_info['center_x'] = sum(x_coords) / len(x_coords)
                card_info['center_y'] = sum(y_coords) / len(y_coords)
                card_info['bottom_y'] = max(y_coords)
                card_info['top_y'] = min(y_coords)
            
            cards.append(card_info)
    
    return cards

def analyze_san_cards_in_frame_229():
    """分析Frame_00229中的"三"卡牌"""
    print("🔍 深度分析Frame_00229中的'三'卡牌分配问题")
    print("=" * 80)
    
    # 加载Frame_00229数据
    frame_229_data = load_frame_data(229)
    if not frame_229_data:
        print("❌ 无法加载Frame_00229数据")
        return
    
    # 提取所有"三"卡牌
    san_cards = extract_cards_by_label(frame_229_data, "三")
    print(f"📊 Frame_00229中的'三'卡牌总数: {len(san_cards)}")
    
    # 按区域分组
    cards_by_region = defaultdict(list)
    for card in san_cards:
        cards_by_region[card['group_id']].append(card)
    
    # 分析区域3的"三"卡牌
    region3_cards = cards_by_region.get(3, [])
    print(f"\n🎯 区域3的'三'卡牌: {len(region3_cards)}张")
    for i, card in enumerate(region3_cards):
        print(f"  {i+1}. 标签: {card['label']}, ID: {card['digital_twin_id']}, 虚拟: {card['is_virtual']}")
        print(f"     区域名称: {card['region_name']}, 所有者: {card['owner']}")
        if 'bottom_y' in card:
            print(f"     位置: bottom_y={card['bottom_y']:.1f}, center=({card['center_x']:.1f}, {card['center_y']:.1f})")
    
    # 分析区域16的"三"卡牌
    region16_cards = cards_by_region.get(16, [])
    print(f"\n🎯 区域16的'三'卡牌: {len(region16_cards)}张")
    for i, card in enumerate(region16_cards):
        print(f"  {i+1}. 标签: {card['label']}, ID: {card['digital_twin_id']}, 虚拟: {card['is_virtual']}")
        print(f"     区域名称: {card['region_name']}, 所有者: {card['owner']}")
        if 'bottom_y' in card:
            print(f"     位置: bottom_y={card['bottom_y']:.1f}, center=({card['center_x']:.1f}, {card['center_y']:.1f})")
    
    # 分析其他区域的"三"卡牌
    other_regions = [region_id for region_id in cards_by_region.keys() if region_id not in [3, 16]]
    if other_regions:
        print(f"\n🎯 其他区域的'三'卡牌:")
        for region_id in sorted(other_regions):
            region_cards = cards_by_region[region_id]
            print(f"  区域{region_id}: {len(region_cards)}张")
            for i, card in enumerate(region_cards):
                print(f"    {i+1}. 标签: {card['label']}, ID: {card['digital_twin_id']}, 虚拟: {card['is_virtual']}")
                print(f"       区域名称: {card['region_name']}, 所有者: {card['owner']}")
    
    # 分析前一帧的情况
    print(f"\n🔍 分析Frame_00228的'三'卡牌情况")
    frame_228_data = load_frame_data(228)
    if frame_228_data:
        san_cards_228 = extract_cards_by_label(frame_228_data, "三")
        cards_by_region_228 = defaultdict(list)
        for card in san_cards_228:
            cards_by_region_228[card['group_id']].append(card)
        
        print(f"  Frame_00228中的'三'卡牌总数: {len(san_cards_228)}")
        for region_id in sorted(cards_by_region_228.keys()):
            region_cards = cards_by_region_228[region_id]
            print(f"  区域{region_id}: {len(region_cards)}张")
            for card in region_cards:
                print(f"    标签: {card['label']}, ID: {card['digital_twin_id']}, 虚拟: {card['is_virtual']}")
    else:
        print("  ❌ 无法加载Frame_00228数据")
    
    # 总结分析结果
    print(f"\n📋 分析总结:")
    print(f"  1. Frame_00229中区域3有{len(region3_cards)}张'三'卡牌")
    print(f"  2. Frame_00229中区域16有{len(region16_cards)}张'三'卡牌")
    if region3_cards and '虚拟' in region3_cards[0]['label']:
        print(f"  3. 区域3中的'三'卡牌是虚拟卡牌: {region3_cards[0]['label']}")
    if len(region16_cards) == 0:
        print(f"  4. 区域16中没有'三'卡牌，无法验证从下到上的1三→2三→3三→虚拟三序列")
    
    print(f"\n🎯 关键发现:")
    if region3_cards and region3_cards[0]['is_virtual']:
        print(f"  ❌ 区域3中存在虚拟'三'卡牌，而非预期的'4三'物理卡牌")
        print(f"  🔍 这可能是导致区域16无法继承'4三'的原因")
    elif len(region3_cards) == 0:
        print(f"  ❌ 区域3中没有'三'卡牌，无法验证'4三'是否存在")
    
    if len(region16_cards) == 0:
        print(f"  ❌ 区域16中没有'三'卡牌，无法验证ID分配问题")

if __name__ == "__main__":
    analyze_san_cards_in_frame_229()
