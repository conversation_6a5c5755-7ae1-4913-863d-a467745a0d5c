#!/usr/bin/env python3
"""
使用正确路径深度分析Frame_00229中区域16的数字孪生ID分配问题
路径：output/calibration_gt_final_with_digital_twin/labels
"""

import json
import os
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter

def load_frame_data(frame_num: int) -> Dict[str, Any]:
    """加载指定帧的数据 - 使用正确路径"""
    frame_path = f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json"
    if os.path.exists(frame_path):
        with open(frame_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def extract_region_cards(frame_data: Dict[str, Any], region_id: int) -> List[Dict[str, Any]]:
    """提取指定区域的卡牌"""
    cards = []
    for shape in frame_data.get('shapes', []):
        if shape.get('group_id') == region_id:
            # 提取关键信息
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'group_id': shape.get('group_id', 0),
                'region_name': shape.get('region_name', ''),
                'points': shape.get('points', []),
                'score': shape.get('score'),
                'owner': shape.get('owner', '')
            }
            
            # 计算位置信息
            if card_info['points']:
                x_coords = [p[0] for p in card_info['points']]
                y_coords = [p[1] for p in card_info['points']]
                card_info['center_x'] = sum(x_coords) / len(x_coords)
                card_info['center_y'] = sum(y_coords) / len(y_coords)
                card_info['bottom_y'] = max(y_coords)
                card_info['top_y'] = min(y_coords)
            
            cards.append(card_info)
    
    return cards

def analyze_frame_229_region16_with_correct_path():
    """使用正确路径分析Frame_00229区域16"""
    print("🔍 使用正确路径分析Frame_00229区域16的数字孪生ID分配问题")
    print("路径: output/calibration_gt_final_with_digital_twin/labels")
    print("=" * 80)
    
    # 加载Frame_00229数据
    frame_229_data = load_frame_data(229)
    if not frame_229_data:
        print("❌ 无法加载Frame_00229数据")
        return
    
    # 提取区域16的所有卡牌
    region16_cards = extract_region_cards(frame_229_data, 16)
    print(f"📊 Frame_00229区域16总卡牌数: {len(region16_cards)}")
    
    if len(region16_cards) == 0:
        print("❌ 区域16中没有卡牌")
        return
    
    # 按标签分组
    cards_by_label = defaultdict(list)
    for card in region16_cards:
        # 提取基础标签（去掉数字前缀）
        label = card['label']
        base_label = label
        if len(label) > 1 and label[0].isdigit():
            base_label = label[1:]  # 去掉数字前缀
        cards_by_label[base_label].append(card)
    
    print(f"\n📋 区域16卡牌按标签分组:")
    for base_label, label_cards in cards_by_label.items():
        print(f"\n🎯 标签'{base_label}': {len(label_cards)}张卡牌")
        
        # 按bottom_y排序（从下到上）
        sorted_cards = sorted(label_cards, key=lambda c: -c.get('bottom_y', 0))
        
        for i, card in enumerate(sorted_cards):
            print(f"  位置{i+1}: {card['label']} -> {card['digital_twin_id']} "
                  f"(bottom_y: {card.get('bottom_y', 0):.1f}, virtual: {card['is_virtual']})")
        
        # 检查是否有"1→2→3→虚拟"的模式
        ids = [card['digital_twin_id'] for card in sorted_cards]
        print(f"  ID序列: {ids}")
        
        # 分析ID模式 - 查找"1三→2三→3三→虚拟三"模式
        if len(ids) >= 4:
            expected_pattern = [f"1{base_label}", f"2{base_label}", f"3{base_label}", f"4{base_label}"]
            actual_pattern = ids[:4]
            
            if actual_pattern[3].startswith("虚拟"):
                print(f"  ❌ 发现问题: 第4张卡牌是虚拟ID而非4{base_label}")
                print(f"     期望: {expected_pattern}")
                print(f"     实际: {actual_pattern}")
                
                # 这就是问题描述中的情况！
                analyze_virtual_id_assignment_issue(base_label, sorted_cards, frame_229_data)
    
    # 统计重复ID
    print(f"\n🔍 重复ID分析:")
    all_ids = [card['digital_twin_id'] for card in region16_cards]
    id_counter = Counter(all_ids)
    
    for twin_id, count in id_counter.items():
        if count > 1:
            print(f"  ❌ 重复ID: {twin_id} 出现{count}次")
    
    # 检查区域3的情况
    print(f"\n🔍 区域3分析:")
    region3_cards = extract_region_cards(frame_229_data, 3)
    print(f"  区域3总卡牌数: {len(region3_cards)}")
    for card in region3_cards:
        print(f"  {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")

def analyze_virtual_id_assignment_issue(base_label: str, sorted_cards: List[Dict[str, Any]], frame_data: Dict[str, Any]):
    """分析虚拟ID分配问题的根本原因"""
    print(f"\n🎯 深度分析'{base_label}'卡牌的虚拟ID分配问题")
    print("-" * 60)
    
    print(f"问题现象:")
    print(f"  当前输出: 区域16从下到上显示 1{base_label} → 2{base_label} → 3{base_label} → 虚拟{base_label}")
    print(f"  设计预期: 区域16从下到上应显示 1{base_label} → 2{base_label} → 3{base_label} → 4{base_label}")
    
    # 检查区域3是否有对应的卡牌
    region3_cards = []
    for shape in frame_data.get('shapes', []):
        if shape.get('group_id') == 3 and base_label in shape.get('label', ''):
            region3_cards.append({
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False)
            })
    
    print(f"\n🔍 区域3中的'{base_label}'卡牌:")
    if region3_cards:
        for card in region3_cards:
            print(f"  {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")
            if card['digital_twin_id'] == f"4{base_label}":
                print(f"  ✅ 找到4{base_label}在区域3中")
            elif card['digital_twin_id'] == f"虚拟{base_label}":
                print(f"  ❌ 区域3中也是虚拟{base_label}，说明4{base_label}从未被分配")
    else:
        print(f"  ❌ 区域3中没有'{base_label}'卡牌")
    
    # 分析全局ID使用情况
    print(f"\n🔍 全局'{base_label}'ID使用情况:")
    all_shapes = frame_data.get('shapes', [])
    global_label_cards = []
    
    for shape in all_shapes:
        if base_label in shape.get('label', ''):
            global_label_cards.append({
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'group_id': shape.get('group_id', 0),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False)
            })
    
    print(f"  全局'{base_label}'卡牌总数: {len(global_label_cards)}")
    
    # 统计ID使用情况
    id_usage = Counter([card['digital_twin_id'] for card in global_label_cards])
    for twin_id in [f"1{base_label}", f"2{base_label}", f"3{base_label}", f"4{base_label}", f"虚拟{base_label}"]:
        count = id_usage.get(twin_id, 0)
        if count > 0:
            print(f"  {twin_id}: {count}次")
            if count > 1:
                print(f"    ❌ 重复使用！")
        else:
            print(f"  {twin_id}: 未使用")
            if twin_id == f"4{base_label}":
                print(f"    🎯 关键发现: 4{base_label}从未被分配，这解释了为什么使用虚拟ID")

def compare_frame_60_and_229():
    """对比Frame_00060和Frame_00229的区域16情况"""
    print(f"\n🔍 对比Frame_00060和Frame_00229的区域16情况")
    print("-" * 60)
    
    # 加载两个frame的数据
    frame_60_data = load_frame_data(60)
    frame_229_data = load_frame_data(229)
    
    if not frame_60_data or not frame_229_data:
        print("❌ 无法加载frame数据")
        return
    
    # 提取区域16的卡牌
    region16_60 = extract_region_cards(frame_60_data, 16)
    region16_229 = extract_region_cards(frame_229_data, 16)
    
    print(f"Frame_00060区域16: {len(region16_60)}张卡牌")
    print(f"Frame_00229区域16: {len(region16_229)}张卡牌")
    
    # 分析Frame_00060的ID分配
    if region16_60:
        print(f"\nFrame_00060区域16详情:")
        sorted_60 = sorted(region16_60, key=lambda c: -c.get('bottom_y', 0))
        for i, card in enumerate(sorted_60):
            print(f"  位置{i+1}: {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")
    
    # 分析Frame_00229的ID分配
    if region16_229:
        print(f"\nFrame_00229区域16详情:")
        sorted_229 = sorted(region16_229, key=lambda c: -c.get('bottom_y', 0))
        for i, card in enumerate(sorted_229[:10]):  # 只显示前10张
            print(f"  位置{i+1}: {card['label']} -> {card['digital_twin_id']} (virtual: {card['is_virtual']})")
        if len(sorted_229) > 10:
            print(f"  ... 还有{len(sorted_229)-10}张卡牌")

if __name__ == "__main__":
    analyze_frame_229_region16_with_correct_path()
    compare_frame_60_and_229()
