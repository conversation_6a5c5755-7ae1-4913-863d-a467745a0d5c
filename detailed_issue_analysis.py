#!/usr/bin/env python3
"""
详细问题分析脚本

深入分析验证发现的问题，找出根本原因
"""

import json
import sys
from pathlib import Path
from collections import defaultdict, Counter

def load_frame_data(frame_num):
    """加载指定帧的数据"""
    frame_path = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_num:05d}.json")
    if not frame_path.exists():
        return None
    
    with open(frame_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_duplicate_id_issue():
    """分析重复ID问题"""
    print("🔍 分析重复ID问题")
    print("=" * 60)
    
    # 检查几个有问题的帧
    problem_frames = [2, 3, 4, 5]
    
    for frame_num in problem_frames:
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            continue
        
        print(f"\n📋 Frame_{frame_num:05d}:")
        
        # 统计ID使用情况
        id_usage = defaultdict(list)
        label_usage = defaultdict(list)
        
        for shape in frame_data.get('shapes', []):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            region_name = shape.get('region_name', '')
            
            if twin_id:
                id_usage[twin_id].append({
                    'label': label,
                    'region': region_name
                })
            
            if label:
                label_usage[label].append({
                    'twin_id': twin_id,
                    'region': region_name
                })
        
        # 检查重复ID
        duplicate_ids = {id_val: cards for id_val, cards in id_usage.items() if len(cards) > 1}
        if duplicate_ids:
            print(f"   ❌ 重复ID: {len(duplicate_ids)}个")
            for id_val, cards in list(duplicate_ids.items())[:3]:  # 只显示前3个
                print(f"     ID '{id_val}': {len(cards)}张卡牌")
                for card in cards:
                    print(f"       - '{card['label']}' in {card['region']}")
        
        # 检查同标签多ID
        multi_id_labels = {label: cards for label, cards in label_usage.items() if len(set(card['twin_id'] for card in cards)) > 1}
        if multi_id_labels:
            print(f"   ⚠️  同标签多ID: {len(multi_id_labels)}个")
            for label, cards in list(multi_id_labels.items())[:3]:  # 只显示前3个
                ids = [card['twin_id'] for card in cards]
                print(f"     标签 '{label}': {len(set(ids))}个不同ID")

def analyze_region_id_issue():
    """分析区域ID问题"""
    print("\n🔍 分析区域ID问题")
    print("=" * 60)
    
    # 检查几个有问题的帧
    problem_frames = [8, 9, 10, 11, 12]
    
    valid_regions = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 16}
    
    for frame_num in problem_frames:
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            continue
        
        print(f"\n📋 Frame_{frame_num:05d}:")
        
        region_usage = defaultdict(int)
        invalid_regions = set()
        
        for shape in frame_data.get('shapes', []):
            group_id = shape.get('group_id', 0)
            region_name = shape.get('region_name', '')
            label = shape.get('label', '')
            
            region_usage[group_id] += 1
            
            if group_id not in valid_regions:
                invalid_regions.add(group_id)
        
        if invalid_regions:
            print(f"   ❌ 无效区域ID: {invalid_regions}")
            for region_id in invalid_regions:
                print(f"     区域{region_id}: {region_usage[region_id]}张卡牌")
        
        print(f"   📊 区域分布: {dict(region_usage)}")

def analyze_temp_id_issue():
    """分析临时ID问题"""
    print("\n🔍 分析临时ID问题")
    print("=" * 60)
    
    # 检查有临时ID问题的帧
    problem_frames = [41, 91]
    
    for frame_num in problem_frames:
        frame_data = load_frame_data(frame_num)
        if not frame_data:
            continue
        
        print(f"\n📋 Frame_{frame_num:05d}:")
        
        temp_ids = []
        
        for shape in frame_data.get('shapes', []):
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            label = shape.get('label', '')
            is_virtual = shape.get('attributes', {}).get('is_virtual', False)
            
            if twin_id.startswith('临时'):
                temp_ids.append({
                    'id': twin_id,
                    'label': label,
                    'is_virtual': is_virtual
                })
        
        if temp_ids:
            print(f"   ⚠️  临时ID: {len(temp_ids)}个")
            for temp_id in temp_ids:
                print(f"     ID '{temp_id['id']}': 标签'{temp_id['label']}', 虚拟={temp_id['is_virtual']}")

def analyze_inheritance_pattern():
    """分析继承模式"""
    print("\n🔍 分析继承模式")
    print("=" * 60)
    
    # 检查连续几帧的继承情况
    frames = [1, 2, 3, 4, 5]
    
    for i in range(len(frames) - 1):
        curr_frame = frames[i]
        next_frame = frames[i + 1]
        
        curr_data = load_frame_data(curr_frame)
        next_data = load_frame_data(next_frame)
        
        if not curr_data or not next_data:
            continue
        
        print(f"\n📋 Frame_{curr_frame:05d} -> Frame_{next_frame:05d}:")
        
        # 提取ID
        curr_ids = set()
        next_ids = set()
        
        for shape in curr_data.get('shapes', []):
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            if twin_id:
                curr_ids.add(twin_id)
        
        for shape in next_data.get('shapes', []):
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            if twin_id:
                next_ids.add(twin_id)
        
        # 分析ID变化
        inherited_ids = curr_ids & next_ids
        new_ids = next_ids - curr_ids
        disappeared_ids = curr_ids - next_ids
        
        print(f"   继承ID: {len(inherited_ids)}")
        print(f"   新增ID: {len(new_ids)}")
        print(f"   消失ID: {len(disappeared_ids)}")
        
        if len(new_ids) > 10:
            print(f"   ⚠️  新增ID过多，可能继承失败")
        
        if len(disappeared_ids) > 10:
            print(f"   ⚠️  消失ID过多，可能有问题")

def identify_root_causes():
    """识别根本原因"""
    print("\n🚨 根本原因分析")
    print("=" * 60)
    
    print("1️⃣ 重复ID问题:")
    print("   - 可能原因: 纯ID继承策略中，多张相同标签的卡牌继承了同一个ID")
    print("   - 影响: 违反了ID唯一性原则")
    print("   - 修复方向: 改进ID分配逻辑，确保每张卡牌都有唯一ID")
    
    print("\n2️⃣ 无效区域ID问题:")
    print("   - 可能原因: group_id字段被错误设置或计算")
    print("   - 影响: 卡牌被分配到不存在的区域")
    print("   - 修复方向: 检查区域ID分配逻辑")
    
    print("\n3️⃣ 临时ID问题:")
    print("   - 可能原因: 暗牌处理后没有正确转换为正式ID")
    print("   - 影响: 暗牌ID格式不符合预期")
    print("   - 修复方向: 完善暗牌ID处理流程")
    
    print("\n4️⃣ 继承失效问题:")
    print("   - 可能原因: 纯ID继承策略实施后，标签匹配作为备选没有正确工作")
    print("   - 影响: 大量卡牌被当作新卡牌处理")
    print("   - 修复方向: 优化继承匹配算法")

def propose_fix_strategy():
    """提出修复策略"""
    print("\n💡 修复策略")
    print("=" * 60)
    
    print("🎯 优先级1: 修复重复ID问题")
    print("   - 在ID分配时检查唯一性")
    print("   - 为重复ID的卡牌重新分配唯一ID")
    print("   - 更新ID映射逻辑")
    
    print("\n🎯 优先级2: 修复区域ID问题")
    print("   - 检查group_id计算逻辑")
    print("   - 确保所有卡牌都分配到有效区域")
    print("   - 修复区域映射错误")
    
    print("\n🎯 优先级3: 完善继承逻辑")
    print("   - 优化位置匹配算法")
    print("   - 改进标签匹配备选机制")
    print("   - 增强跨区域继承规则")
    
    print("\n🎯 优先级4: 修复临时ID问题")
    print("   - 完善暗牌ID转换流程")
    print("   - 确保所有临时ID都能正确转换")

def main():
    """主函数"""
    print("🔍 详细问题分析")
    print("=" * 60)
    
    # 分析各类问题
    analyze_duplicate_id_issue()
    analyze_region_id_issue()
    analyze_temp_id_issue()
    analyze_inheritance_pattern()
    
    # 识别根本原因
    identify_root_causes()
    
    # 提出修复策略
    propose_fix_strategy()

if __name__ == "__main__":
    main()
